import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { getTheme, motionVariants, spacing, ThemeType } from '@/styles/design-system';

interface ThemedSectionProps {
  theme?: ThemeType;
  children: React.ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary' | 'card' | 'glass';
  size?: 'normal' | 'large';
  withPattern?: boolean;
  withParallax?: boolean;
  id?: string;
  animate?: boolean;
  animationVariant?: 'fadeInUp' | 'fadeInLeft' | 'fadeInRight' | 'scaleIn';
}

const ThemedSection: React.FC<ThemedSectionProps> = ({
  theme = 'neutral',
  children,
  className,
  variant = 'primary',
  size = 'normal',
  withPattern = true,
  withParallax = false,
  id,
  animate = true,
  animationVariant = 'fadeInUp'
}) => {
  const themeConfig = getTheme(theme);
  
  const getBackgroundClass = () => {
    switch (variant) {
      case 'secondary':
        return themeConfig.secondaryGradient;
      case 'card':
        return themeConfig.cardGradient;
      case 'glass':
        return themeConfig.glass;
      default:
        return themeConfig.primaryGradient;
    }
  };

  const getSizeClass = () => {
    return size === 'large' ? spacing.sectionLarge : spacing.section;
  };

  const sectionContent = (
    <section
      id={id}
      className={cn(
        'relative overflow-hidden',
        getSizeClass(),
        getBackgroundClass(),
        className
      )}
    >
      {/* Background Pattern */}
      {withPattern && (
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-[url('/patterns/dots.svg')]"></div>
        </div>
      )}

      {/* Animated Gradient Overlay */}
      <div className={cn(
        'absolute inset-0 opacity-20 transition-opacity duration-1000',
        themeConfig.overlayGradient
      )}></div>

      {/* Parallax Background Elements */}
      {withParallax && (
        <>
          <motion.div
            className={cn(
              'absolute top-10 right-10 w-64 h-64 rounded-full blur-3xl opacity-30',
              theme === 'hardware' ? 'bg-green-400/20' : 
              theme === 'software' ? 'bg-orange-400/20' : 'bg-gray-400/20'
            )}
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.2, 0.4, 0.2]
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              repeatType: "reverse"
            }}
          />
          <motion.div
            className={cn(
              'absolute bottom-10 left-10 w-48 h-48 rounded-full blur-3xl opacity-20',
              theme === 'hardware' ? 'bg-green-500/20' : 
              theme === 'software' ? 'bg-orange-500/20' : 'bg-gray-500/20'
            )}
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.1, 0.3, 0.1]
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: "reverse",
              delay: 2
            }}
          />
        </>
      )}

      {/* Content Container */}
      <div className={cn('relative z-10', spacing.container)}>
        {children}
      </div>

      {/* Bottom Accent Line */}
      <motion.div
        className={cn(
          'absolute bottom-0 left-0 h-1 w-full',
          theme === 'hardware' ? 'bg-gradient-to-r from-transparent via-green-400 to-transparent' :
          theme === 'software' ? 'bg-gradient-to-r from-transparent via-orange-400 to-transparent' :
          'bg-gradient-to-r from-transparent via-gray-400 to-transparent'
        )}
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        transition={{ duration: 1.5, delay: 0.5 }}
        viewport={{ once: true }}
      />
    </section>
  );

  if (!animate) {
    return sectionContent;
  }

  return (
    <motion.div
      initial={motionVariants[animationVariant].initial}
      whileInView={motionVariants[animationVariant].animate}
      transition={motionVariants[animationVariant].transition}
      viewport={{ once: true, margin: "-100px" }}
    >
      {sectionContent}
    </motion.div>
  );
};

export default ThemedSection;
