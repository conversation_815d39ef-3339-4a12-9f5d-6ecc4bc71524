import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { getTheme, motionVariants, spacing, ThemeType } from '@/styles/design-system';

interface EnhancedCardProps {
  theme?: ThemeType;
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'glass' | 'elevated' | 'minimal';
  size?: 'small' | 'normal' | 'large';
  withGlow?: boolean;
  withHover?: boolean;
  withBorder?: boolean;
  animate?: boolean;
  delay?: number;
}

const EnhancedCard: React.FC<EnhancedCardProps> = ({
  theme = 'neutral',
  children,
  className,
  variant = 'default',
  size = 'normal',
  withGlow = false,
  withHover = true,
  withBorder = true,
  animate = true,
  delay = 0
}) => {
  const themeConfig = getTheme(theme);

  const getVariantClasses = () => {
    switch (variant) {
      case 'glass':
        return cn(
          themeConfig.glass,
          'border backdrop-blur-xl',
          withBorder && themeConfig.border
        );
      case 'elevated':
        return cn(
          themeConfig.cardGradient,
          'shadow-2xl border',
          withBorder && themeConfig.border,
          withGlow && themeConfig.glow
        );
      case 'minimal':
        return cn(
          'bg-white/60 dark:bg-gray-800/60',
          'border border-gray-200/40 dark:border-gray-700/40'
        );
      default:
        return cn(
          themeConfig.cardGradient,
          'shadow-xl border',
          withBorder && themeConfig.border,
          themeConfig.shadow
        );
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return spacing.cardSmall;
      case 'large':
        return 'p-10';
      default:
        return spacing.card;
    }
  };

  const getHoverClasses = () => {
    if (!withHover) return '';
    return cn(
      'transition-all duration-300 ease-out',
      'hover:-translate-y-2 hover:scale-[1.02]',
      themeConfig.hoverShadow,
      withGlow && `hover:${themeConfig.glow}`
    );
  };

  const cardContent = (
    <div
      className={cn(
        'relative overflow-hidden rounded-2xl',
        getVariantClasses(),
        getSizeClasses(),
        getHoverClasses(),
        className
      )}
    >
      {/* Decorative Elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('/patterns/dots.svg')]"></div>
      </div>

      {/* Gradient Overlay */}
      <div className={cn(
        'absolute inset-0 opacity-10 transition-opacity duration-500',
        themeConfig.overlayGradient
      )}></div>

      {/* Corner Accent */}
      <div className={cn(
        'absolute top-0 right-0 w-20 h-20 opacity-20 rounded-bl-2xl',
        theme === 'hardware' ? 'bg-gradient-to-bl from-green-400 to-transparent' :
        theme === 'software' ? 'bg-gradient-to-bl from-orange-400 to-transparent' :
        'bg-gradient-to-bl from-gray-400 to-transparent'
      )}></div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>

      {/* Interactive Glow Effect */}
      {withHover && (
        <div className={cn(
          'absolute inset-0 rounded-2xl opacity-0 transition-opacity duration-300',
          'hover:opacity-100 pointer-events-none',
          theme === 'hardware' ? 'shadow-[inset_0_0_30px_rgba(34,197,94,0.15)]' :
          theme === 'software' ? 'shadow-[inset_0_0_30px_rgba(251,146,60,0.15)]' :
          'shadow-[inset_0_0_30px_rgba(107,114,128,0.15)]'
        )}></div>
      )}
    </div>
  );

  if (!animate) {
    return cardContent;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      whileInView={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ 
        duration: 0.6, 
        ease: "easeOut",
        delay: delay 
      }}
      viewport={{ once: true, margin: "-50px" }}
      whileHover={{ 
        scale: withHover ? 1.02 : 1,
        transition: { duration: 0.2 }
      }}
    >
      {cardContent}
    </motion.div>
  );
};

export default EnhancedCard;
