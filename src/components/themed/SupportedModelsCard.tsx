import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import ThemedCard from './ThemedCard';
import SupportedModels from '@/sections/SupportedModels';

interface SupportedModelsCardProps {
  theme?: 'software' | 'hardware';
  className?: string;
}

const SupportedModelsCard: React.FC<SupportedModelsCardProps> = ({ 
  theme = 'software',
  className 
}) => {
  const getThemeConfig = () => {
    if (theme === 'hardware') {
      return {
        title: "Supported Device Models",
        subtitle: "Comprehensive hardware compatibility for professional repair and diagnostics",
        icon: "🔧",
        accentColor: "text-green-600 dark:text-green-400"
      };
    }
    return {
      title: "Supported Device Models", 
      subtitle: "Complete software compatibility for unlocking and repair solutions",
      icon: "📱",
      accentColor: "text-pegasus-orange"
    };
  };

  const config = getThemeConfig();

  return (
    <div className={cn("w-full", className)}>
      <ThemedCard
        theme={theme}
        title={config.title}
        subtitle={config.subtitle}
        className="max-w-7xl mx-auto"
      >
        {/* Icon and description section */}
        <motion.div 
          className="flex items-center gap-4 mb-8 p-6 rounded-xl bg-white/50 dark:bg-gray-800/50 border border-gray-200/30 dark:border-gray-700/30"
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <div className="text-4xl">{config.icon}</div>
          <div>
            <h3 className={cn("text-xl font-semibold mb-1", config.accentColor)}>
              Professional Device Support
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Browse our extensive database of supported devices with detailed compatibility information
            </p>
          </div>
        </motion.div>

        {/* SupportedModels component */}
        <div className="relative">
          <SupportedModels theme={theme} />
        </div>

        {/* Bottom accent bar */}
        <motion.div 
          className={cn(
            "mt-8 h-1 rounded-full",
            theme === 'hardware' 
              ? "bg-gradient-to-r from-green-400 to-green-600" 
              : "bg-gradient-to-r from-orange-400 to-orange-600"
          )}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        />
      </ThemedCard>
    </div>
  );
};

export default SupportedModelsCard;
