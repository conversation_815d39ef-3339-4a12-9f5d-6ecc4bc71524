import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Star, Shield, Zap } from 'lucide-react';
import ThemedCard from './ThemedCard';
import Pricing from '@/sections/Pricing';

interface PricingCardProps {
  theme?: 'software' | 'hardware';
  className?: string;
}

const PricingCard: React.FC<PricingCardProps> = ({ 
  theme = 'software',
  className 
}) => {
  const getThemeConfig = () => {
    if (theme === 'hardware') {
      return {
        title: "Hardware Solutions Pricing",
        subtitle: "Professional hardware documentation and repair guides",
        icon: "⚡",
        accentColor: "text-green-600 dark:text-green-400",
        features: [
          { icon: Shield, text: "Circuit Diagrams & Schematics" },
          { icon: Zap, text: "Component Specifications" },
          { icon: Star, text: "Professional Support" }
        ]
      };
    }
    return {
      title: "Software Solutions Pricing",
      subtitle: "Complete software tools for device unlocking and repair",
      icon: "💎",
      accentColor: "text-pegasus-orange",
      features: [
        { icon: Shield, text: "Advanced Unlocking Tools" },
        { icon: Zap, text: "Real-time Updates" },
        { icon: Star, text: "24/7 Technical Support" }
      ]
    };
  };

  const config = getThemeConfig();

  return (
    <div className={cn("w-full", className)}>
      <ThemedCard
        theme={theme}
        title={config.title}
        subtitle={config.subtitle}
        className="max-w-7xl mx-auto"
      >
        {/* Features highlight section */}
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          {config.features.map((feature, index) => (
            <motion.div
              key={index}
              className="flex items-center gap-3 p-4 rounded-xl bg-white/60 dark:bg-gray-800/60 border border-gray-200/40 dark:border-gray-700/40 hover:bg-white/80 dark:hover:bg-gray-800/80 transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.02 }}
            >
              <div className={cn(
                "p-2 rounded-lg",
                theme === 'hardware' 
                  ? "bg-green-100 dark:bg-green-900/30" 
                  : "bg-orange-100 dark:bg-orange-900/30"
              )}>
                <feature.icon className={cn("h-5 w-5", config.accentColor)} />
              </div>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {feature.text}
              </span>
            </motion.div>
          ))}
        </motion.div>

        {/* Main pricing content */}
        <div className="relative">
          <Pricing theme={theme} />
        </div>

        {/* Bottom call-to-action section */}
        <motion.div 
          className={cn(
            "mt-8 p-6 rounded-xl border-2 border-dashed",
            theme === 'hardware' 
              ? "border-green-300 dark:border-green-700 bg-green-50/50 dark:bg-green-900/20" 
              : "border-orange-300 dark:border-orange-700 bg-orange-50/50 dark:bg-orange-900/20"
          )}
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">{config.icon}</div>
            <h4 className={cn("text-lg font-semibold mb-2", config.accentColor)}>
              Need a Custom Solution?
            </h4>
            <p className="text-gray-600 dark:text-gray-300 text-sm">
              Contact our team for enterprise pricing and custom integrations tailored to your business needs.
            </p>
          </div>
        </motion.div>
      </ThemedCard>
    </div>
  );
};

export default PricingCard;
