import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface ThemedCardProps {
  theme?: 'software' | 'hardware';
  children: React.ReactNode;
  className?: string;
  title?: string;
  subtitle?: string;
  showHeader?: boolean;
}

const ThemedCard: React.FC<ThemedCardProps> = ({
  theme = 'software',
  children,
  className,
  title,
  subtitle,
  showHeader = true
}) => {
  const getThemeStyles = () => {
    if (theme === 'hardware') {
      return {
        background: 'bg-gradient-to-br from-green-50/80 to-green-100/60 dark:from-gray-800/90 dark:to-gray-900/80',
        border: 'border-green-200/60 dark:border-green-900/40',
        shadow: 'shadow-green-100/50 dark:shadow-green-900/20',
        hover: 'hover:shadow-green-200/60 dark:hover:shadow-green-800/30',
        titleGradient: 'bg-gradient-to-r from-green-600 to-green-400 bg-clip-text text-transparent',
        accent: 'text-green-600 dark:text-green-400'
      };
    }
    return {
      background: 'bg-gradient-to-br from-orange-50/80 to-orange-100/60 dark:from-gray-800/90 dark:to-gray-900/80',
      border: 'border-orange-200/60 dark:border-orange-900/40',
      shadow: 'shadow-orange-100/50 dark:shadow-orange-900/20',
      hover: 'hover:shadow-orange-200/60 dark:hover:shadow-orange-800/30',
      titleGradient: 'bg-gradient-to-r from-orange-600 to-orange-400 bg-clip-text text-transparent',
      accent: 'text-pegasus-orange'
    };
  };

  const themeStyles = getThemeStyles();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      viewport={{ once: true, margin: "-50px" }}
      className={cn(
        "relative overflow-hidden rounded-2xl border backdrop-blur-sm",
        "transition-all duration-300 ease-out",
        themeStyles.background,
        themeStyles.border,
        `shadow-xl ${themeStyles.shadow}`,
        `hover:shadow-2xl ${themeStyles.hover}`,
        "hover:-translate-y-1",
        className
      )}
    >
      {/* Decorative background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[url('/patterns/dots.svg')]"></div>
      </div>

      {/* Animated gradient overlay */}
      <div className={cn(
        "absolute inset-0 opacity-10 transition-opacity duration-500",
        theme === 'hardware'
          ? "bg-gradient-to-br from-green-400/20 via-transparent to-green-600/20"
          : "bg-gradient-to-br from-orange-400/20 via-transparent to-orange-600/20"
      )}></div>

      {/* Corner accent */}
      <div className={cn(
        "absolute top-0 right-0 w-20 h-20 opacity-20",
        theme === 'hardware'
          ? "bg-gradient-to-bl from-green-400 to-transparent"
          : "bg-gradient-to-bl from-orange-400 to-transparent"
      )}></div>

      {/* Header section */}
      {showHeader && (title || subtitle) && (
        <div className="relative px-8 pt-8 pb-6 border-b border-gray-200/50 dark:border-gray-700/50">
          {title && (
            <h2 className={cn(
              "text-3xl font-bold mb-2",
              themeStyles.titleGradient
            )}>
              {title}
            </h2>
          )}
          {subtitle && (
            <p className="text-gray-600 dark:text-gray-300 text-lg">
              {subtitle}
            </p>
          )}
        </div>
      )}

      {/* Content section */}
      <div className="relative p-8">
        {children}
      </div>

      {/* Subtle glow effect */}
      <div className={cn(
        "absolute inset-0 rounded-2xl opacity-0 transition-opacity duration-300",
        "hover:opacity-100 pointer-events-none",
        theme === 'hardware'
          ? "shadow-[inset_0_0_20px_rgba(34,197,94,0.1)]"
          : "shadow-[inset_0_0_20px_rgba(251,146,60,0.1)]"
      )}></div>
    </motion.div>
  );
};

export default ThemedCard;
