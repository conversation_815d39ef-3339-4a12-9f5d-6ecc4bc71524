
import React from 'react';
import { motion } from 'framer-motion';
import HardwareHero from '@/components/hardware/HardwareHero';
import CircuitDiagrams from '@/components/hardware/CircuitDiagrams';
import CircuitComponents from '@/components/hardware/CircuitComponents';
import SupportedModels from '@/sections/SupportedModels';
import Pricing from '@/sections/Pricing';

const Hardware = () => {
  return (
    <div className="hardware-page">
      {/* Hardware Hero Section */}
      <HardwareHero />
      
      {/* Circuit Diagrams Section */}
      <CircuitDiagrams />
      
      {/* Circuit Components Section */}
      <CircuitComponents />
      
      {/* Supported Models Section - reusing from Software */}
      <section id="hardware-supported-models" className="py-20 bg-gradient-to-r from-green-50 to-green-100/50 dark:from-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-center mb-16">
              <span className="bg-gradient-to-r from-green-600 to-green-400 bg-clip-text text-transparent">
                Supported Models
              </span>
            </h2>
            <SupportedModels />
          </motion.div>
        </div>
      </section>
      
      {/* Pricing Section - reusing from Software */}
      <section id="hardware-pricing" className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-center mb-16">
              <span className="bg-gradient-to-r from-green-600 to-green-400 bg-clip-text text-transparent">
                Pricing Plans
              </span>
            </h2>
            <Pricing />
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Hardware;
