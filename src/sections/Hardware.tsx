
import React from 'react';
import { motion } from 'framer-motion';
import HardwareHero from '@/components/hardware/HardwareHero';
import CircuitDiagrams from '@/components/hardware/CircuitDiagrams';
import CircuitComponents from '@/components/hardware/CircuitComponents';
import SupportedModelsCard from '@/components/themed/SupportedModelsCard';
import PricingCard from '@/components/themed/PricingCard';

const Hardware = () => {
  return (
    <div className="hardware-page">
      {/* Hardware Hero Section */}
      <HardwareHero />

      {/* Circuit Diagrams Section */}
      <CircuitDiagrams />

      {/* Circuit Components Section */}
      <CircuitComponents />

      {/* Supported Models Section */}
      <section id="hardware-supported-models" className="py-20 bg-gradient-to-r from-green-50 to-green-100/50 dark:from-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-4">
          <SupportedModelsCard theme="hardware" />
        </div>
      </section>

      {/* Pricing Section */}
      <section id="hardware-pricing" className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <PricingCard theme="hardware" />
        </div>
      </section>
    </div>
  );
};

export default Hardware;
