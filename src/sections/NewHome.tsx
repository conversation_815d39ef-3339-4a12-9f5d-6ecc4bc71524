
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { Download, ArrowRight, ChevronRight } from "lucide-react";
import SectionHeader from '@/components/SectionHeader';
import AnimatedCard from '@/components/AnimatedCard';
import AnimatedCounter from '@/components/AnimatedCounter';
import Text3D from '@/components/3D/Text3D';
import { use3DEffect } from '@/hooks/use3DEffect';
import PaymentMethods from '@/sections/PaymentMethods';
import Resellers from '@/sections/Resellers';
import Contact from '@/sections/Contact';

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
};

const NewHome = () => {
  const navigate = useNavigate();
  const { toast: toastNotify } = useToast();
  const [stats, setStats] = useState({
    totalModels: 0,
    downloadCount: 0,
    distributorsCount: 0
  });

  const leftCardRef = use3DEffect({
    intensity: 10,
    perspective: 1000,
    glare: true
  });

  const rightCardRef = use3DEffect({
    intensity: 10,
    perspective: 1000,
    glare: true
  });

  useEffect(() => {
    // Fetch statistics
    const fetchStats = async () => {
      try {
        // Get total models count
        const { data: modelSettings, error: modelError } = await supabase
          .from('settings')
          .select('numeric_value')
          .eq('key', 'total_models')
          .single();

        if (modelError) console.error('Error fetching total models:', modelError);

        // Get distributors count
        const { data: distributorSettings, error: distributorError } = await supabase
          .from('settings')
          .select('numeric_value')
          .eq('key', 'distributors_count')
          .single();

        if (distributorError) console.error('Error fetching distributors count:', distributorError);

        // Get download count
        const { data: updateData, error: updateError } = await supabase
          .from('update')
          .select('download_count')
          .order('release_at', { ascending: false })
          .limit(1);

        if (updateError) console.error('Error fetching download count:', updateError);

        setStats({
          totalModels: modelSettings?.numeric_value || 0,
          downloadCount: updateData?.[0]?.download_count || 0,
          distributorsCount: distributorSettings?.numeric_value || 0
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
      }
    };

    fetchStats();
  }, []);

  const navigateTo = (path: string) => {
    navigate(path);
  };

  return (
    <div>
      {/* Hero Section */}
      <section className="pt-28 pb-20 bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 overflow-hidden relative">
        <div className="absolute inset-0 bg-[url('/patterns/dots.svg')] opacity-5"></div>

        {/* Animated background elements */}
        <motion.div
          className="absolute top-20 right-20 w-64 h-64 bg-gradient-to-r from-pegasus-orange/5 to-green-500/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.2, 0.3]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        ></motion.div>
        <motion.div
          className="absolute bottom-10 left-10 w-40 h-40 bg-gradient-to-r from-pegasus-orange/10 to-green-500/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.2, 0.3, 0.2]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        ></motion.div>

        <div className="container mx-auto px-4 relative">
          <motion.div
            className="text-center max-w-4xl mx-auto mb-16"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
          >
            <Text3D
              as="h1"
              size="4xl"
              depth="deep"
              color="text-white"
              className="mb-6 font-montserrat tracking-tight leading-tight"
            >
              Complete <span className="text-pegasus-orange">Software</span> & <span className="text-green-500">Hardware</span> Solution
            </Text3D>
            <p className="text-xl text-gray-300 mb-10">
              Pegasus Tool is the ultimate smartphone repair and unlocking system with integrated software and hardware solutions for professionals.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8 items-stretch">
            {/* Software Card */}
            <motion.div
              ref={leftCardRef}
              className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-xl p-8 rounded-2xl border border-pegasus-orange/20 shadow-lg transform-gpu preserve-3d"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
            >
              <div className="h-full flex flex-col">
                <div className="flex-shrink-0 mb-4">
                  <div className="w-16 h-16 rounded-full bg-pegasus-orange/20 flex items-center justify-center mb-4">
                    <Download className="h-8 w-8 text-pegasus-orange" />
                  </div>
                  <h2 className="text-2xl font-bold text-pegasus-orange mb-2">Software Solution</h2>
                  <p className="text-gray-300 mb-6">
                    Unlock, flash, and repair smartphones with our powerful software tools supporting a wide range of models from popular brands.
                  </p>
                </div>

                <div className="mt-auto">
                  <Button
                    className="w-full bg-pegasus-orange hover:bg-pegasus-orange-600 text-white rounded-full py-6 transition-all duration-300 hover:-translate-y-1 shadow-lg hover:shadow-xl flex items-center justify-center group"
                    onClick={() => navigateTo("/software")}
                  >
                    <ArrowRight className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                    <span>Explore Software</span>
                  </Button>
                </div>
              </div>
            </motion.div>

            {/* Hardware Card */}
            <motion.div
              ref={rightCardRef}
              className="bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-xl p-8 rounded-2xl border border-green-500/20 shadow-lg transform-gpu preserve-3d"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.4 }}
            >
              <div className="h-full flex flex-col">
                <div className="flex-shrink-0 mb-4">
                  <div className="w-16 h-16 rounded-full bg-green-500/20 flex items-center justify-center mb-4">
                    <Download className="h-8 w-8 text-green-500" />
                  </div>
                  <h2 className="text-2xl font-bold text-green-500 mb-2">Hardware Solution</h2>
                  <p className="text-gray-300 mb-6">
                    Access detailed circuit diagrams, component specifications, and hardware repair guides for advanced device servicing.
                  </p>
                </div>

                <div className="mt-auto">
                  <Button
                    className="w-full bg-green-500 hover:bg-green-600 text-white rounded-full py-6 transition-all duration-300 hover:-translate-y-1 shadow-lg hover:shadow-xl flex items-center justify-center group"
                    onClick={() => navigateTo("/hardware")}
                  >
                    <ArrowRight className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                    <span>Explore Hardware</span>
                  </Button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Statistics Section (former "Our Numbers Are Talking") */}
      <section className="py-20 bg-white dark:bg-gray-900 overflow-hidden">
        <div className="container mx-auto px-4">
          <SectionHeader
            title="Our Statistics"
            subtitle="Discover the impact and reach of Pegasus Tool"
            highlightWord="Statistics"
          />

          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-10 mt-16"
            variants={container}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.div variants={item}>
              <AnimatedCard variant="elegant" hoverEffect="lift" delay={0.1} className="p-8 border-t-4 border-gradient-to-r from-pegasus-orange to-green-500">
                <div className="flex flex-col items-center text-center">
                  <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-orange-100 to-green-100 dark:from-orange-900/30 dark:to-green-900/20 rounded-full mb-6 shadow-md">
                    <Download className="h-8 w-8 text-pegasus-orange" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Supported Models</h3>
                  <p className="text-4xl font-bold text-pegasus-orange mt-2 mb-4">
                    <AnimatedCounter value={stats.totalModels} />
                  </p>
                  <p className="text-gray-600 dark:text-gray-300">Compatible smartphone models</p>
                </div>
              </AnimatedCard>
            </motion.div>

            <motion.div variants={item}>
              <AnimatedCard variant="elegant" hoverEffect="lift" delay={0.2} className="p-8 border-t-4 border-gradient-to-r from-pegasus-orange to-green-500">
                <div className="flex flex-col items-center text-center">
                  <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-orange-100 to-green-100 dark:from-orange-900/30 dark:to-green-900/20 rounded-full mb-6 shadow-md">
                    <Download className="h-8 w-8 text-pegasus-orange" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Total Downloads</h3>
                  <p className="text-4xl font-bold text-green-500 mt-2 mb-4">
                    <AnimatedCounter value={stats.downloadCount} />
                  </p>
                  <p className="text-gray-600 dark:text-gray-300">Professionals using our solution</p>
                </div>
              </AnimatedCard>
            </motion.div>

            <motion.div variants={item}>
              <AnimatedCard variant="elegant" hoverEffect="lift" delay={0.3} className="p-8 border-t-4 border-gradient-to-r from-pegasus-orange to-green-500">
                <div className="flex flex-col items-center text-center">
                  <div className="w-16 h-16 flex items-center justify-center bg-gradient-to-br from-orange-100 to-green-100 dark:from-orange-900/30 dark:to-green-900/20 rounded-full mb-6 shadow-md">
                    <Download className="h-8 w-8 text-pegasus-orange" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-white">Official Distributors</h3>
                  <p className="text-4xl font-bold text-pegasus-orange mt-2 mb-4">
                    <AnimatedCounter value={stats.distributorsCount} />
                  </p>
                  <p className="text-gray-600 dark:text-gray-300">Authorized distributors worldwide</p>
                </div>
              </AnimatedCard>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section id="why-choose-us" className="py-20 bg-gradient-to-r from-orange-50 to-green-50/50 dark:from-gray-800 dark:to-gray-900 overflow-hidden relative">
        <div className="absolute inset-0 bg-[url('/patterns/circles.svg')] opacity-5"></div>

        <div className="container mx-auto px-4 relative">
          <SectionHeader
            title="Why Choose Pegasus Tool"
            subtitle="The complete solution for device unlocking and repair services"
            highlightWord="Pegasus"
          />

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto"
            variants={container}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true, margin: "-100px" }}
          >
            <motion.div variants={item}>
              <AnimatedCard variant="gradient" hoverEffect="scale" className="p-6 border border-orange-200/30 dark:border-orange-700/10">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 flex-shrink-0 rounded-full bg-gradient-to-br from-orange-200 to-orange-300 dark:from-orange-700/30 dark:to-orange-600/20 flex items-center justify-center shadow-md">
                    <ChevronRight className="h-6 w-6 text-pegasus-orange" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-white">Complete Solution</h3>
                    <p className="text-gray-600 dark:text-gray-300">Integrated software and hardware tools provide a comprehensive repair and unlocking experience.</p>
                  </div>
                </div>
              </AnimatedCard>
            </motion.div>

            <motion.div variants={item}>
              <AnimatedCard variant="gradient" hoverEffect="scale" className="p-6 border border-green-200/30 dark:border-green-700/10">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 flex-shrink-0 rounded-full bg-gradient-to-br from-green-200 to-green-300 dark:from-green-700/30 dark:to-green-600/20 flex items-center justify-center shadow-md">
                    <ChevronRight className="h-6 w-6 text-green-500" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-white">Technical Expertise</h3>
                    <p className="text-gray-600 dark:text-gray-300">Access to professional-grade tools with detailed documentation and technical support.</p>
                  </div>
                </div>
              </AnimatedCard>
            </motion.div>

            <motion.div variants={item}>
              <AnimatedCard variant="gradient" hoverEffect="scale" className="p-6 border border-orange-200/30 dark:border-orange-700/10">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 flex-shrink-0 rounded-full bg-gradient-to-br from-orange-200 to-orange-300 dark:from-orange-700/30 dark:to-orange-600/20 flex items-center justify-center shadow-md">
                    <ChevronRight className="h-6 w-6 text-pegasus-orange" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-white">Regular Updates</h3>
                    <p className="text-gray-600 dark:text-gray-300">Frequent software updates and new hardware documentation to stay current with the latest devices.</p>
                  </div>
                </div>
              </AnimatedCard>
            </motion.div>

            <motion.div variants={item}>
              <AnimatedCard variant="gradient" hoverEffect="scale" className="p-6 border border-green-200/30 dark:border-green-700/10">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 flex-shrink-0 rounded-full bg-gradient-to-br from-green-200 to-green-300 dark:from-green-700/30 dark:to-green-600/20 flex items-center justify-center shadow-md">
                    <ChevronRight className="h-6 w-6 text-green-500" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-white">Global Support</h3>
                    <p className="text-gray-600 dark:text-gray-300">Worldwide network of distributors and technical support available in multiple languages.</p>
                  </div>
                </div>
              </AnimatedCard>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-orange-600 via-orange-500 to-green-600 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('/patterns/grid.svg')] opacity-10"></div>

        {/* Animated particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-white/10"
            style={{
              width: Math.random() * 50 + 20,
              height: Math.random() * 50 + 20,
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -100, 0],
              x: [0, Math.random() * 50 - 25, 0],
              opacity: [0, 0.5, 0],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              delay: Math.random() * 5,
            }}
          />
        ))}

        <div className="container mx-auto px-4 relative">
          <div className="text-center max-w-3xl mx-auto mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">Ready to get started?</h2>
            <p className="text-lg text-white/90 mb-8">
              Choose the solution that best fits your needs - complete software tools, hardware documentation, or both.
            </p>
          </div>

          <div className="flex flex-col md:flex-row items-center justify-center gap-6">
            <Button
              className="bg-white text-orange-600 hover:bg-orange-50 px-8 py-4 rounded-full text-lg shadow-lg transition-all duration-300 hover:-translate-y-1 flex items-center w-full md:w-auto justify-center group"
              onClick={() => navigateTo("/software")}
            >
              <span className="relative">Software Solutions</span>
            </Button>

            <Button
              className="bg-white text-green-600 hover:bg-green-50 px-8 py-4 rounded-full text-lg shadow-lg transition-all duration-300 hover:-translate-y-1 flex items-center w-full md:w-auto justify-center group"
              onClick={() => navigateTo("/hardware")}
            >
              <span className="relative">Hardware Documentation</span>
            </Button>
          </div>
        </div>
      </section>

      {/* Payment Methods Section */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
          >
            <PaymentMethods />
          </motion.div>
        </div>
      </section>

      {/* Resellers Section */}
      <section className="py-20 bg-gradient-to-r from-orange-50 to-orange-100/50 dark:from-gray-800 dark:to-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
          >
            <Resellers />
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true }}
          >
            <Contact />
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default NewHome;
